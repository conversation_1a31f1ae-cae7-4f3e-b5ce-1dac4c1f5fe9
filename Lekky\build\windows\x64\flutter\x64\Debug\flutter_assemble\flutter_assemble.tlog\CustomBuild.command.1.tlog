^D:\000.WORKSPACE\LEKKY\BUILD\WINDOWS\X64\CMAKEFILES\26FA69EAD63E454842839013B2BA6582\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\src\flutter PROJECT_DIR=D:\000.Workspace\Lekky FLUTTER_ROOT=C:\src\flutter FLUTTER_EPHEMERAL_DIR=D:\000.Workspace\Lekky\windows\flutter\ephemeral PROJECT_DIR=D:\000.Workspace\Lekky FLUTTER_TARGET=D:\000.Workspace\Lekky\lib\main.dart DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9lNzZjOTU2NDk4ODQxZTFhYjQ1ODU3N2QzODkyMDAzZTU1M2U0ZjNjLw== DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\000.Workspace\Lekky\.dart_tool\package_config.json C:/src/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\000.WORKSPACE\LEKKY\BUILD\WINDOWS\X64\CMAKEFILES\FE40E904489FFEC55D73A5BED5A0CE11\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\000.WORKSPACE\LEKKY\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/000.Workspace/Lekky/windows -BD:/000.Workspace/Lekky/build/windows/x64 --check-stamp-file D:/000.Workspace/Lekky/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
