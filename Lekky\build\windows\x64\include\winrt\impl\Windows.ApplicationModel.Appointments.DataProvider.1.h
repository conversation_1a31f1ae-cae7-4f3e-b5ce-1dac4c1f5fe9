// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Appointments_DataProvider_1_H
#define WINRT_Windows_ApplicationModel_Appointments_DataProvider_1_H
#include "winrt/impl/Windows.ApplicationModel.Appointments.DataProvider.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Appointments::DataProvider
{
    struct __declspec(empty_bases) IAppointmentCalendarCancelMeetingRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarCancelMeetingRequest>
    {
        IAppointmentCalendarCancelMeetingRequest(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarCancelMeetingRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarCancelMeetingRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarCancelMeetingRequestEventArgs>
    {
        IAppointmentCalendarCancelMeetingRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarCancelMeetingRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarCreateOrUpdateAppointmentRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarCreateOrUpdateAppointmentRequest>
    {
        IAppointmentCalendarCreateOrUpdateAppointmentRequest(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarCreateOrUpdateAppointmentRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs>
    {
        IAppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarForwardMeetingRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarForwardMeetingRequest>
    {
        IAppointmentCalendarForwardMeetingRequest(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarForwardMeetingRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarForwardMeetingRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarForwardMeetingRequestEventArgs>
    {
        IAppointmentCalendarForwardMeetingRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarForwardMeetingRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarProposeNewTimeForMeetingRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarProposeNewTimeForMeetingRequest>
    {
        IAppointmentCalendarProposeNewTimeForMeetingRequest(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarProposeNewTimeForMeetingRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarProposeNewTimeForMeetingRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarProposeNewTimeForMeetingRequestEventArgs>
    {
        IAppointmentCalendarProposeNewTimeForMeetingRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarProposeNewTimeForMeetingRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarSyncManagerSyncRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarSyncManagerSyncRequest>
    {
        IAppointmentCalendarSyncManagerSyncRequest(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarSyncManagerSyncRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarSyncManagerSyncRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarSyncManagerSyncRequestEventArgs>
    {
        IAppointmentCalendarSyncManagerSyncRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarSyncManagerSyncRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarUpdateMeetingResponseRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarUpdateMeetingResponseRequest>
    {
        IAppointmentCalendarUpdateMeetingResponseRequest(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarUpdateMeetingResponseRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentCalendarUpdateMeetingResponseRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentCalendarUpdateMeetingResponseRequestEventArgs>
    {
        IAppointmentCalendarUpdateMeetingResponseRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppointmentCalendarUpdateMeetingResponseRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentDataProviderConnection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentDataProviderConnection>
    {
        IAppointmentDataProviderConnection(std::nullptr_t = nullptr) noexcept {}
        IAppointmentDataProviderConnection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppointmentDataProviderTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentDataProviderTriggerDetails>
    {
        IAppointmentDataProviderTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IAppointmentDataProviderTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
