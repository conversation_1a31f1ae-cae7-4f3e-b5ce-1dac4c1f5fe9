// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Appointments_DataProvider_2_H
#define WINRT_Windows_ApplicationModel_Appointments_DataProvider_2_H
#include "winrt/impl/Windows.ApplicationModel.Appointments.DataProvider.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Appointments::DataProvider
{
    struct __declspec(empty_bases) AppointmentCalendarCancelMeetingRequest : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarCancelMeetingRequest
    {
        AppointmentCalendarCancelMeetingRequest(std::nullptr_t) noexcept {}
        AppointmentCalendarCancelMeetingRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarCancelMeetingRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarCancelMeetingRequestEventArgs : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarCancelMeetingRequestEventArgs
    {
        AppointmentCalendarCancelMeetingRequestEventArgs(std::nullptr_t) noexcept {}
        AppointmentCalendarCancelMeetingRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarCancelMeetingRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarCreateOrUpdateAppointmentRequest : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarCreateOrUpdateAppointmentRequest
    {
        AppointmentCalendarCreateOrUpdateAppointmentRequest(std::nullptr_t) noexcept {}
        AppointmentCalendarCreateOrUpdateAppointmentRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarCreateOrUpdateAppointmentRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs
    {
        AppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs(std::nullptr_t) noexcept {}
        AppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarCreateOrUpdateAppointmentRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarForwardMeetingRequest : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarForwardMeetingRequest
    {
        AppointmentCalendarForwardMeetingRequest(std::nullptr_t) noexcept {}
        AppointmentCalendarForwardMeetingRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarForwardMeetingRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarForwardMeetingRequestEventArgs : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarForwardMeetingRequestEventArgs
    {
        AppointmentCalendarForwardMeetingRequestEventArgs(std::nullptr_t) noexcept {}
        AppointmentCalendarForwardMeetingRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarForwardMeetingRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarProposeNewTimeForMeetingRequest : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarProposeNewTimeForMeetingRequest
    {
        AppointmentCalendarProposeNewTimeForMeetingRequest(std::nullptr_t) noexcept {}
        AppointmentCalendarProposeNewTimeForMeetingRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarProposeNewTimeForMeetingRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarProposeNewTimeForMeetingRequestEventArgs : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarProposeNewTimeForMeetingRequestEventArgs
    {
        AppointmentCalendarProposeNewTimeForMeetingRequestEventArgs(std::nullptr_t) noexcept {}
        AppointmentCalendarProposeNewTimeForMeetingRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarProposeNewTimeForMeetingRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarSyncManagerSyncRequest : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarSyncManagerSyncRequest
    {
        AppointmentCalendarSyncManagerSyncRequest(std::nullptr_t) noexcept {}
        AppointmentCalendarSyncManagerSyncRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarSyncManagerSyncRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarSyncManagerSyncRequestEventArgs : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarSyncManagerSyncRequestEventArgs
    {
        AppointmentCalendarSyncManagerSyncRequestEventArgs(std::nullptr_t) noexcept {}
        AppointmentCalendarSyncManagerSyncRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarSyncManagerSyncRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarUpdateMeetingResponseRequest : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarUpdateMeetingResponseRequest
    {
        AppointmentCalendarUpdateMeetingResponseRequest(std::nullptr_t) noexcept {}
        AppointmentCalendarUpdateMeetingResponseRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarUpdateMeetingResponseRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentCalendarUpdateMeetingResponseRequestEventArgs : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarUpdateMeetingResponseRequestEventArgs
    {
        AppointmentCalendarUpdateMeetingResponseRequestEventArgs(std::nullptr_t) noexcept {}
        AppointmentCalendarUpdateMeetingResponseRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentCalendarUpdateMeetingResponseRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentDataProviderConnection : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentDataProviderConnection
    {
        AppointmentDataProviderConnection(std::nullptr_t) noexcept {}
        AppointmentDataProviderConnection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentDataProviderConnection(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) AppointmentDataProviderTriggerDetails : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentDataProviderTriggerDetails
    {
        AppointmentDataProviderTriggerDetails(std::nullptr_t) noexcept {}
        AppointmentDataProviderTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Appointments::DataProvider::IAppointmentDataProviderTriggerDetails(ptr, take_ownership_from_abi) {}
    };
}
#endif
