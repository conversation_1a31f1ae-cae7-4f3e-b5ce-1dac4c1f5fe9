// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Core_1_H
#define WINRT_Windows_ApplicationModel_Core_1_H
#include "winrt/impl/Windows.ApplicationModel.Core.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Core
{
    struct __declspec(empty_bases) IAppListEntry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppListEntry>
    {
        IAppListEntry(std::nullptr_t = nullptr) noexcept {}
        IAppListEntry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppListEntry2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppListEntry2>
    {
        IAppListEntry2(std::nullptr_t = nullptr) noexcept {}
        IAppListEntry2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppListEntry3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppListEntry3>
    {
        IAppListEntry3(std::nullptr_t = nullptr) noexcept {}
        IAppListEntry3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplication :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplication>
    {
        ICoreApplication(std::nullptr_t = nullptr) noexcept {}
        ICoreApplication(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplication2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplication2>
    {
        ICoreApplication2(std::nullptr_t = nullptr) noexcept {}
        ICoreApplication2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplication3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplication3>
    {
        ICoreApplication3(std::nullptr_t = nullptr) noexcept {}
        ICoreApplication3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationExit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationExit>
    {
        ICoreApplicationExit(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationExit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationUnhandledError :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationUnhandledError>
    {
        ICoreApplicationUnhandledError(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationUnhandledError(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationUseCount :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationUseCount>
    {
        ICoreApplicationUseCount(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationUseCount(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView>
    {
        ICoreApplicationView(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView2>
    {
        ICoreApplicationView2(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationView3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView3>
    {
        ICoreApplicationView3(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationView5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView5>
    {
        ICoreApplicationView5(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationView6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView6>
    {
        ICoreApplicationView6(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreApplicationViewTitleBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationViewTitleBar>
    {
        ICoreApplicationViewTitleBar(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationViewTitleBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreImmersiveApplication :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreImmersiveApplication>
    {
        ICoreImmersiveApplication(std::nullptr_t = nullptr) noexcept {}
        ICoreImmersiveApplication(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreImmersiveApplication2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreImmersiveApplication2>
    {
        ICoreImmersiveApplication2(std::nullptr_t = nullptr) noexcept {}
        ICoreImmersiveApplication2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreImmersiveApplication3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreImmersiveApplication3>
    {
        ICoreImmersiveApplication3(std::nullptr_t = nullptr) noexcept {}
        ICoreImmersiveApplication3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameworkView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkView>
    {
        IFrameworkView(std::nullptr_t = nullptr) noexcept {}
        IFrameworkView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameworkViewSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkViewSource>
    {
        IFrameworkViewSource(std::nullptr_t = nullptr) noexcept {}
        IFrameworkViewSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHostedViewClosingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHostedViewClosingEventArgs>
    {
        IHostedViewClosingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHostedViewClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUnhandledError :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnhandledError>
    {
        IUnhandledError(std::nullptr_t = nullptr) noexcept {}
        IUnhandledError(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUnhandledErrorDetectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnhandledErrorDetectedEventArgs>
    {
        IUnhandledErrorDetectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUnhandledErrorDetectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
