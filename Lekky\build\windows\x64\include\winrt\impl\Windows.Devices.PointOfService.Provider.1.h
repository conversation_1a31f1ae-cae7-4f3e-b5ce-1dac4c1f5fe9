// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Devices_PointOfService_Provider_1_H
#define WINRT_Windows_Devices_PointOfService_Provider_1_H
#include "winrt/impl/Windows.Devices.PointOfService.Provider.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::PointOfService::Provider
{
    struct __declspec(empty_bases) IBarcodeScannerDisableScannerRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerDisableScannerRequest>
    {
        IBarcodeScannerDisableScannerRequest(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerDisableScannerRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerDisableScannerRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerDisableScannerRequest2>
    {
        IBarcodeScannerDisableScannerRequest2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerDisableScannerRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerDisableScannerRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerDisableScannerRequestEventArgs>
    {
        IBarcodeScannerDisableScannerRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerDisableScannerRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerEnableScannerRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerEnableScannerRequest>
    {
        IBarcodeScannerEnableScannerRequest(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerEnableScannerRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerEnableScannerRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerEnableScannerRequest2>
    {
        IBarcodeScannerEnableScannerRequest2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerEnableScannerRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerEnableScannerRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerEnableScannerRequestEventArgs>
    {
        IBarcodeScannerEnableScannerRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerEnableScannerRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerFrameReader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerFrameReader>
    {
        IBarcodeScannerFrameReader(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerFrameReader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerFrameReaderFrameArrivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerFrameReaderFrameArrivedEventArgs>
    {
        IBarcodeScannerFrameReaderFrameArrivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerFrameReaderFrameArrivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerGetSymbologyAttributesRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerGetSymbologyAttributesRequest>
    {
        IBarcodeScannerGetSymbologyAttributesRequest(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerGetSymbologyAttributesRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerGetSymbologyAttributesRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerGetSymbologyAttributesRequest2>
    {
        IBarcodeScannerGetSymbologyAttributesRequest2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerGetSymbologyAttributesRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerGetSymbologyAttributesRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerGetSymbologyAttributesRequestEventArgs>
    {
        IBarcodeScannerGetSymbologyAttributesRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerGetSymbologyAttributesRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerHideVideoPreviewRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerHideVideoPreviewRequest>
    {
        IBarcodeScannerHideVideoPreviewRequest(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerHideVideoPreviewRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerHideVideoPreviewRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerHideVideoPreviewRequest2>
    {
        IBarcodeScannerHideVideoPreviewRequest2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerHideVideoPreviewRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerHideVideoPreviewRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerHideVideoPreviewRequestEventArgs>
    {
        IBarcodeScannerHideVideoPreviewRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerHideVideoPreviewRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerProviderConnection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerProviderConnection>
    {
        IBarcodeScannerProviderConnection(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerProviderConnection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerProviderConnection2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerProviderConnection2>
    {
        IBarcodeScannerProviderConnection2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerProviderConnection2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerProviderTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerProviderTriggerDetails>
    {
        IBarcodeScannerProviderTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerProviderTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerSetActiveSymbologiesRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerSetActiveSymbologiesRequest>
    {
        IBarcodeScannerSetActiveSymbologiesRequest(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerSetActiveSymbologiesRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerSetActiveSymbologiesRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerSetActiveSymbologiesRequest2>
    {
        IBarcodeScannerSetActiveSymbologiesRequest2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerSetActiveSymbologiesRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerSetActiveSymbologiesRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerSetActiveSymbologiesRequestEventArgs>
    {
        IBarcodeScannerSetActiveSymbologiesRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerSetActiveSymbologiesRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerSetSymbologyAttributesRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerSetSymbologyAttributesRequest>
    {
        IBarcodeScannerSetSymbologyAttributesRequest(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerSetSymbologyAttributesRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerSetSymbologyAttributesRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerSetSymbologyAttributesRequest2>
    {
        IBarcodeScannerSetSymbologyAttributesRequest2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerSetSymbologyAttributesRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerSetSymbologyAttributesRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerSetSymbologyAttributesRequestEventArgs>
    {
        IBarcodeScannerSetSymbologyAttributesRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerSetSymbologyAttributesRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerStartSoftwareTriggerRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerStartSoftwareTriggerRequest>
    {
        IBarcodeScannerStartSoftwareTriggerRequest(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerStartSoftwareTriggerRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerStartSoftwareTriggerRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerStartSoftwareTriggerRequest2>
    {
        IBarcodeScannerStartSoftwareTriggerRequest2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerStartSoftwareTriggerRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerStartSoftwareTriggerRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerStartSoftwareTriggerRequestEventArgs>
    {
        IBarcodeScannerStartSoftwareTriggerRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerStartSoftwareTriggerRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerStopSoftwareTriggerRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerStopSoftwareTriggerRequest>
    {
        IBarcodeScannerStopSoftwareTriggerRequest(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerStopSoftwareTriggerRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerStopSoftwareTriggerRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerStopSoftwareTriggerRequest2>
    {
        IBarcodeScannerStopSoftwareTriggerRequest2(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerStopSoftwareTriggerRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerStopSoftwareTriggerRequestEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerStopSoftwareTriggerRequestEventArgs>
    {
        IBarcodeScannerStopSoftwareTriggerRequestEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerStopSoftwareTriggerRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeScannerVideoFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeScannerVideoFrame>
    {
        IBarcodeScannerVideoFrame(std::nullptr_t = nullptr) noexcept {}
        IBarcodeScannerVideoFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBarcodeSymbologyAttributesBuilder :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarcodeSymbologyAttributesBuilder>
    {
        IBarcodeSymbologyAttributesBuilder(std::nullptr_t = nullptr) noexcept {}
        IBarcodeSymbologyAttributesBuilder(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
