// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Graphics_Printing_Workflow_2_H
#define WINRT_Windows_Graphics_Printing_Workflow_2_H
#include "winrt/impl/Windows.ApplicationModel.Activation.1.h"
#include "winrt/impl/Windows.Graphics.Printing.Workflow.1.h"
WINRT_EXPORT namespace winrt::Windows::Graphics::Printing::Workflow
{
    struct __declspec(empty_bases) PrintWorkflowBackgroundSession : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowBackgroundSession
    {
        PrintWorkflowBackgroundSession(std::nullptr_t) noexcept {}
        PrintWorkflowBackgroundSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowBackgroundSession(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowBackgroundSetupRequestedEventArgs : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowBackgroundSetupRequestedEventArgs
    {
        PrintWorkflowBackgroundSetupRequestedEventArgs(std::nullptr_t) noexcept {}
        PrintWorkflowBackgroundSetupRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowBackgroundSetupRequestedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowConfiguration : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowConfiguration
    {
        PrintWorkflowConfiguration(std::nullptr_t) noexcept {}
        PrintWorkflowConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowConfiguration(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowForegroundSession : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowForegroundSession
    {
        PrintWorkflowForegroundSession(std::nullptr_t) noexcept {}
        PrintWorkflowForegroundSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowForegroundSession(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowForegroundSetupRequestedEventArgs : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowForegroundSetupRequestedEventArgs
    {
        PrintWorkflowForegroundSetupRequestedEventArgs(std::nullptr_t) noexcept {}
        PrintWorkflowForegroundSetupRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowForegroundSetupRequestedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowObjectModelSourceFileContent : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowObjectModelSourceFileContent
    {
        PrintWorkflowObjectModelSourceFileContent(std::nullptr_t) noexcept {}
        PrintWorkflowObjectModelSourceFileContent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowObjectModelSourceFileContent(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowObjectModelTargetPackage : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowObjectModelTargetPackage
    {
        PrintWorkflowObjectModelTargetPackage(std::nullptr_t) noexcept {}
        PrintWorkflowObjectModelTargetPackage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowObjectModelTargetPackage(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowSourceContent : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowSourceContent
    {
        PrintWorkflowSourceContent(std::nullptr_t) noexcept {}
        PrintWorkflowSourceContent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowSourceContent(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowSpoolStreamContent : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowSpoolStreamContent
    {
        PrintWorkflowSpoolStreamContent(std::nullptr_t) noexcept {}
        PrintWorkflowSpoolStreamContent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowSpoolStreamContent(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowStreamTarget : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowStreamTarget
    {
        PrintWorkflowStreamTarget(std::nullptr_t) noexcept {}
        PrintWorkflowStreamTarget(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowStreamTarget(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowSubmittedEventArgs : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowSubmittedEventArgs
    {
        PrintWorkflowSubmittedEventArgs(std::nullptr_t) noexcept {}
        PrintWorkflowSubmittedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowSubmittedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowSubmittedOperation : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowSubmittedOperation
    {
        PrintWorkflowSubmittedOperation(std::nullptr_t) noexcept {}
        PrintWorkflowSubmittedOperation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowSubmittedOperation(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowTarget : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowTarget
    {
        PrintWorkflowTarget(std::nullptr_t) noexcept {}
        PrintWorkflowTarget(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowTarget(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowTriggerDetails : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowTriggerDetails
    {
        PrintWorkflowTriggerDetails(std::nullptr_t) noexcept {}
        PrintWorkflowTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowTriggerDetails(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowUIActivatedEventArgs : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowUIActivatedEventArgs
    {
        PrintWorkflowUIActivatedEventArgs(std::nullptr_t) noexcept {}
        PrintWorkflowUIActivatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowUIActivatedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) PrintWorkflowXpsDataAvailableEventArgs : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowXpsDataAvailableEventArgs
    {
        PrintWorkflowXpsDataAvailableEventArgs(std::nullptr_t) noexcept {}
        PrintWorkflowXpsDataAvailableEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Graphics::Printing::Workflow::IPrintWorkflowXpsDataAvailableEventArgs(ptr, take_ownership_from_abi) {}
    };
}
#endif
