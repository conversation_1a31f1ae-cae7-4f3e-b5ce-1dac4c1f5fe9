// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_System_1_H
#define WINRT_Windows_System_1_H
#include "winrt/impl/Windows.System.0.h"
WINRT_EXPORT namespace winrt::Windows::System
{
    struct __declspec(empty_bases) IAppActivationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppActivationResult>
    {
        IAppActivationResult(std::nullptr_t = nullptr) noexcept {}
        IAppActivationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppDiagnosticInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDiagnosticInfo>
    {
        IAppDiagnosticInfo(std::nullptr_t = nullptr) noexcept {}
        IAppDiagnosticInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppDiagnosticInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDiagnosticInfo2>
    {
        IAppDiagnosticInfo2(std::nullptr_t = nullptr) noexcept {}
        IAppDiagnosticInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppDiagnosticInfo3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDiagnosticInfo3>
    {
        IAppDiagnosticInfo3(std::nullptr_t = nullptr) noexcept {}
        IAppDiagnosticInfo3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppDiagnosticInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDiagnosticInfoStatics>
    {
        IAppDiagnosticInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IAppDiagnosticInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppDiagnosticInfoStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDiagnosticInfoStatics2>
    {
        IAppDiagnosticInfoStatics2(std::nullptr_t = nullptr) noexcept {}
        IAppDiagnosticInfoStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppDiagnosticInfoWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDiagnosticInfoWatcher>
    {
        IAppDiagnosticInfoWatcher(std::nullptr_t = nullptr) noexcept {}
        IAppDiagnosticInfoWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppDiagnosticInfoWatcherEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDiagnosticInfoWatcherEventArgs>
    {
        IAppDiagnosticInfoWatcherEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppDiagnosticInfoWatcherEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppExecutionStateChangeResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppExecutionStateChangeResult>
    {
        IAppExecutionStateChangeResult(std::nullptr_t = nullptr) noexcept {}
        IAppExecutionStateChangeResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppMemoryReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppMemoryReport>
    {
        IAppMemoryReport(std::nullptr_t = nullptr) noexcept {}
        IAppMemoryReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppMemoryReport2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppMemoryReport2>
    {
        IAppMemoryReport2(std::nullptr_t = nullptr) noexcept {}
        IAppMemoryReport2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppMemoryUsageLimitChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppMemoryUsageLimitChangingEventArgs>
    {
        IAppMemoryUsageLimitChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppMemoryUsageLimitChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppResourceGroupBackgroundTaskReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppResourceGroupBackgroundTaskReport>
    {
        IAppResourceGroupBackgroundTaskReport(std::nullptr_t = nullptr) noexcept {}
        IAppResourceGroupBackgroundTaskReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppResourceGroupInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppResourceGroupInfo>
    {
        IAppResourceGroupInfo(std::nullptr_t = nullptr) noexcept {}
        IAppResourceGroupInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppResourceGroupInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppResourceGroupInfo2>
    {
        IAppResourceGroupInfo2(std::nullptr_t = nullptr) noexcept {}
        IAppResourceGroupInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppResourceGroupInfoWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppResourceGroupInfoWatcher>
    {
        IAppResourceGroupInfoWatcher(std::nullptr_t = nullptr) noexcept {}
        IAppResourceGroupInfoWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppResourceGroupInfoWatcherEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppResourceGroupInfoWatcherEventArgs>
    {
        IAppResourceGroupInfoWatcherEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppResourceGroupInfoWatcherEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs>
    {
        IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppResourceGroupInfoWatcherExecutionStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppResourceGroupMemoryReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppResourceGroupMemoryReport>
    {
        IAppResourceGroupMemoryReport(std::nullptr_t = nullptr) noexcept {}
        IAppResourceGroupMemoryReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppResourceGroupStateReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppResourceGroupStateReport>
    {
        IAppResourceGroupStateReport(std::nullptr_t = nullptr) noexcept {}
        IAppResourceGroupStateReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppUriHandlerHost :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppUriHandlerHost>
    {
        IAppUriHandlerHost(std::nullptr_t = nullptr) noexcept {}
        IAppUriHandlerHost(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppUriHandlerHostFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppUriHandlerHostFactory>
    {
        IAppUriHandlerHostFactory(std::nullptr_t = nullptr) noexcept {}
        IAppUriHandlerHostFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppUriHandlerRegistration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppUriHandlerRegistration>
    {
        IAppUriHandlerRegistration(std::nullptr_t = nullptr) noexcept {}
        IAppUriHandlerRegistration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppUriHandlerRegistrationManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppUriHandlerRegistrationManager>
    {
        IAppUriHandlerRegistrationManager(std::nullptr_t = nullptr) noexcept {}
        IAppUriHandlerRegistrationManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppUriHandlerRegistrationManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppUriHandlerRegistrationManagerStatics>
    {
        IAppUriHandlerRegistrationManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IAppUriHandlerRegistrationManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDateTimeSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDateTimeSettingsStatics>
    {
        IDateTimeSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IDateTimeSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDispatcherQueue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherQueue>
    {
        IDispatcherQueue(std::nullptr_t = nullptr) noexcept {}
        IDispatcherQueue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDispatcherQueue2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherQueue2>
    {
        IDispatcherQueue2(std::nullptr_t = nullptr) noexcept {}
        IDispatcherQueue2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDispatcherQueueController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherQueueController>
    {
        IDispatcherQueueController(std::nullptr_t = nullptr) noexcept {}
        IDispatcherQueueController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDispatcherQueueControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherQueueControllerStatics>
    {
        IDispatcherQueueControllerStatics(std::nullptr_t = nullptr) noexcept {}
        IDispatcherQueueControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDispatcherQueueShutdownStartingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherQueueShutdownStartingEventArgs>
    {
        IDispatcherQueueShutdownStartingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDispatcherQueueShutdownStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDispatcherQueueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherQueueStatics>
    {
        IDispatcherQueueStatics(std::nullptr_t = nullptr) noexcept {}
        IDispatcherQueueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDispatcherQueueTimer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherQueueTimer>
    {
        IDispatcherQueueTimer(std::nullptr_t = nullptr) noexcept {}
        IDispatcherQueueTimer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFolderLauncherOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFolderLauncherOptions>
    {
        IFolderLauncherOptions(std::nullptr_t = nullptr) noexcept {}
        IFolderLauncherOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownUserPropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownUserPropertiesStatics>
    {
        IKnownUserPropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownUserPropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILaunchUriResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILaunchUriResult>
    {
        ILaunchUriResult(std::nullptr_t = nullptr) noexcept {}
        ILaunchUriResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherOptions>
    {
        ILauncherOptions(std::nullptr_t = nullptr) noexcept {}
        ILauncherOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherOptions2>
    {
        ILauncherOptions2(std::nullptr_t = nullptr) noexcept {}
        ILauncherOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherOptions3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherOptions3>
    {
        ILauncherOptions3(std::nullptr_t = nullptr) noexcept {}
        ILauncherOptions3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherOptions4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherOptions4>
    {
        ILauncherOptions4(std::nullptr_t = nullptr) noexcept {}
        ILauncherOptions4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherStatics>
    {
        ILauncherStatics(std::nullptr_t = nullptr) noexcept {}
        ILauncherStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherStatics2>
    {
        ILauncherStatics2(std::nullptr_t = nullptr) noexcept {}
        ILauncherStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherStatics3>
    {
        ILauncherStatics3(std::nullptr_t = nullptr) noexcept {}
        ILauncherStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherStatics4>
    {
        ILauncherStatics4(std::nullptr_t = nullptr) noexcept {}
        ILauncherStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherStatics5>
    {
        ILauncherStatics5(std::nullptr_t = nullptr) noexcept {}
        ILauncherStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherUIOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherUIOptions>
    {
        ILauncherUIOptions(std::nullptr_t = nullptr) noexcept {}
        ILauncherUIOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILauncherViewOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILauncherViewOptions>
    {
        ILauncherViewOptions(std::nullptr_t = nullptr) noexcept {}
        ILauncherViewOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMemoryManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMemoryManagerStatics>
    {
        IMemoryManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IMemoryManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMemoryManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMemoryManagerStatics2>
    {
        IMemoryManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IMemoryManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMemoryManagerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMemoryManagerStatics3>
    {
        IMemoryManagerStatics3(std::nullptr_t = nullptr) noexcept {}
        IMemoryManagerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMemoryManagerStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMemoryManagerStatics4>
    {
        IMemoryManagerStatics4(std::nullptr_t = nullptr) noexcept {}
        IMemoryManagerStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProcessLauncherOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessLauncherOptions>
    {
        IProcessLauncherOptions(std::nullptr_t = nullptr) noexcept {}
        IProcessLauncherOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProcessLauncherResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessLauncherResult>
    {
        IProcessLauncherResult(std::nullptr_t = nullptr) noexcept {}
        IProcessLauncherResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProcessLauncherStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessLauncherStatics>
    {
        IProcessLauncherStatics(std::nullptr_t = nullptr) noexcept {}
        IProcessLauncherStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProcessMemoryReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessMemoryReport>
    {
        IProcessMemoryReport(std::nullptr_t = nullptr) noexcept {}
        IProcessMemoryReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProtocolForResultsOperation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtocolForResultsOperation>
    {
        IProtocolForResultsOperation(std::nullptr_t = nullptr) noexcept {}
        IProtocolForResultsOperation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRemoteLauncherOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteLauncherOptions>
    {
        IRemoteLauncherOptions(std::nullptr_t = nullptr) noexcept {}
        IRemoteLauncherOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRemoteLauncherStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteLauncherStatics>
    {
        IRemoteLauncherStatics(std::nullptr_t = nullptr) noexcept {}
        IRemoteLauncherStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShutdownManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShutdownManagerStatics>
    {
        IShutdownManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IShutdownManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShutdownManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShutdownManagerStatics2>,
        impl::require<winrt::Windows::System::IShutdownManagerStatics2, winrt::Windows::System::IShutdownManagerStatics>
    {
        IShutdownManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IShutdownManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimeZoneSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimeZoneSettingsStatics>
    {
        ITimeZoneSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        ITimeZoneSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimeZoneSettingsStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimeZoneSettingsStatics2>
    {
        ITimeZoneSettingsStatics2(std::nullptr_t = nullptr) noexcept {}
        ITimeZoneSettingsStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUser :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUser>
    {
        IUser(std::nullptr_t = nullptr) noexcept {}
        IUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserAuthenticationStatusChangeDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserAuthenticationStatusChangeDeferral>
    {
        IUserAuthenticationStatusChangeDeferral(std::nullptr_t = nullptr) noexcept {}
        IUserAuthenticationStatusChangeDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserAuthenticationStatusChangingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserAuthenticationStatusChangingEventArgs>
    {
        IUserAuthenticationStatusChangingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUserAuthenticationStatusChangingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserChangedEventArgs>
    {
        IUserChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUserChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserChangedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserChangedEventArgs2>
    {
        IUserChangedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IUserChangedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserDeviceAssociationChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserDeviceAssociationChangedEventArgs>
    {
        IUserDeviceAssociationChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUserDeviceAssociationChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserDeviceAssociationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserDeviceAssociationStatics>
    {
        IUserDeviceAssociationStatics(std::nullptr_t = nullptr) noexcept {}
        IUserDeviceAssociationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserPicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserPicker>
    {
        IUserPicker(std::nullptr_t = nullptr) noexcept {}
        IUserPicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserPickerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserPickerStatics>
    {
        IUserPickerStatics(std::nullptr_t = nullptr) noexcept {}
        IUserPickerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserStatics>
    {
        IUserStatics(std::nullptr_t = nullptr) noexcept {}
        IUserStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserWatcher>
    {
        IUserWatcher(std::nullptr_t = nullptr) noexcept {}
        IUserWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
