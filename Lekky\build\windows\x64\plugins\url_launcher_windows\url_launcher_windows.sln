﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{5E9511F2-0A8D-3121-B2C9-DA727206BAF3}"
	ProjectSection(ProjectDependencies) = postProject
		{D89C28F1-0DC3-382B-9089-1F93558CDB59} = {D89C28F1-0DC3-382B-9089-1F93558CDB59}
		{1F7E54A6-C4DE-34B9-904E-10FC4466611E} = {1F7E54A6-C4DE-34B9-904E-10FC4466611E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{C8EE8AE8-C636-3BF0-BE05-EF0F3C34E6A1}"
	ProjectSection(ProjectDependencies) = postProject
		{5E9511F2-0A8D-3121-B2C9-DA727206BAF3} = {5E9511F2-0A8D-3121-B2C9-DA727206BAF3}
		{D89C28F1-0DC3-382B-9089-1F93558CDB59} = {D89C28F1-0DC3-382B-9089-1F93558CDB59}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{D89C28F1-0DC3-382B-9089-1F93558CDB59}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{AA0D9B00-1855-3C0D-BF57-15C991F317E2}"
	ProjectSection(ProjectDependencies) = postProject
		{D89C28F1-0DC3-382B-9089-1F93558CDB59} = {D89C28F1-0DC3-382B-9089-1F93558CDB59}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A}"
	ProjectSection(ProjectDependencies) = postProject
		{D89C28F1-0DC3-382B-9089-1F93558CDB59} = {D89C28F1-0DC3-382B-9089-1F93558CDB59}
		{AA0D9B00-1855-3C0D-BF57-15C991F317E2} = {AA0D9B00-1855-3C0D-BF57-15C991F317E2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "url_launcher_windows_plugin", "url_launcher_windows_plugin.vcxproj", "{1F7E54A6-C4DE-34B9-904E-10FC4466611E}"
	ProjectSection(ProjectDependencies) = postProject
		{D89C28F1-0DC3-382B-9089-1F93558CDB59} = {D89C28F1-0DC3-382B-9089-1F93558CDB59}
		{AA0D9B00-1855-3C0D-BF57-15C991F317E2} = {AA0D9B00-1855-3C0D-BF57-15C991F317E2}
		{08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A} = {08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5E9511F2-0A8D-3121-B2C9-DA727206BAF3}.Debug|x64.ActiveCfg = Debug|x64
		{5E9511F2-0A8D-3121-B2C9-DA727206BAF3}.Debug|x64.Build.0 = Debug|x64
		{5E9511F2-0A8D-3121-B2C9-DA727206BAF3}.Profile|x64.ActiveCfg = Profile|x64
		{5E9511F2-0A8D-3121-B2C9-DA727206BAF3}.Profile|x64.Build.0 = Profile|x64
		{5E9511F2-0A8D-3121-B2C9-DA727206BAF3}.Release|x64.ActiveCfg = Release|x64
		{5E9511F2-0A8D-3121-B2C9-DA727206BAF3}.Release|x64.Build.0 = Release|x64
		{C8EE8AE8-C636-3BF0-BE05-EF0F3C34E6A1}.Debug|x64.ActiveCfg = Debug|x64
		{C8EE8AE8-C636-3BF0-BE05-EF0F3C34E6A1}.Profile|x64.ActiveCfg = Profile|x64
		{C8EE8AE8-C636-3BF0-BE05-EF0F3C34E6A1}.Release|x64.ActiveCfg = Release|x64
		{D89C28F1-0DC3-382B-9089-1F93558CDB59}.Debug|x64.ActiveCfg = Debug|x64
		{D89C28F1-0DC3-382B-9089-1F93558CDB59}.Debug|x64.Build.0 = Debug|x64
		{D89C28F1-0DC3-382B-9089-1F93558CDB59}.Profile|x64.ActiveCfg = Profile|x64
		{D89C28F1-0DC3-382B-9089-1F93558CDB59}.Profile|x64.Build.0 = Profile|x64
		{D89C28F1-0DC3-382B-9089-1F93558CDB59}.Release|x64.ActiveCfg = Release|x64
		{D89C28F1-0DC3-382B-9089-1F93558CDB59}.Release|x64.Build.0 = Release|x64
		{AA0D9B00-1855-3C0D-BF57-15C991F317E2}.Debug|x64.ActiveCfg = Debug|x64
		{AA0D9B00-1855-3C0D-BF57-15C991F317E2}.Debug|x64.Build.0 = Debug|x64
		{AA0D9B00-1855-3C0D-BF57-15C991F317E2}.Profile|x64.ActiveCfg = Profile|x64
		{AA0D9B00-1855-3C0D-BF57-15C991F317E2}.Profile|x64.Build.0 = Profile|x64
		{AA0D9B00-1855-3C0D-BF57-15C991F317E2}.Release|x64.ActiveCfg = Release|x64
		{AA0D9B00-1855-3C0D-BF57-15C991F317E2}.Release|x64.Build.0 = Release|x64
		{08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A}.Debug|x64.ActiveCfg = Debug|x64
		{08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A}.Debug|x64.Build.0 = Debug|x64
		{08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A}.Profile|x64.ActiveCfg = Profile|x64
		{08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A}.Profile|x64.Build.0 = Profile|x64
		{08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A}.Release|x64.ActiveCfg = Release|x64
		{08D8905F-5F4C-31E2-ACA0-F6B8D8A70C8A}.Release|x64.Build.0 = Release|x64
		{1F7E54A6-C4DE-34B9-904E-10FC4466611E}.Debug|x64.ActiveCfg = Debug|x64
		{1F7E54A6-C4DE-34B9-904E-10FC4466611E}.Debug|x64.Build.0 = Debug|x64
		{1F7E54A6-C4DE-34B9-904E-10FC4466611E}.Profile|x64.ActiveCfg = Profile|x64
		{1F7E54A6-C4DE-34B9-904E-10FC4466611E}.Profile|x64.Build.0 = Profile|x64
		{1F7E54A6-C4DE-34B9-904E-10FC4466611E}.Release|x64.ActiveCfg = Release|x64
		{1F7E54A6-C4DE-34B9-904E-10FC4466611E}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {25875751-B119-381C-8EE4-1622605B3FBF}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
