// File: lib/core/constants/app_constants.dart
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // Main Routes
  static const String routeHome = '/home';
  static const String routeWelcome = '/welcome';
  static const String routeSplash = '/splash';
  static const String routeSetup = '/setup';
  static const String routeValidationDashboard = '/validation-dashboard';

  // Main Tab Routes
  static const String routeHistory = '/history';
  static const String routeCost = '/cost';
  static const String routeMainSettings = '/main-settings';

  // Settings Routes
  static const String routeSettings = '/settings';
  static const String routeCsvExport = '/settings/csv/export';
  static const String routeCsvImport = '/settings/csv/import';
  static const String routeDeleteAllData = '/settings/data/delete-all';
  static const String routeAbout = '/settings/about';
  static const String routeDonate = '/settings/donate';
  static const String routeDonateOptions = '/settings/donate/options';

  // Appearance Routes
  static const String routeAppearance = '/settings/appearance';
  static const String routeTheme = '/settings/appearance/theme';

  // Region Routes
  static const String routeRegion = '/settings/region';
  static const String routeLanguage = '/settings/region/language';
  static const String routeCurrency = '/settings/region/currency';

  // Date Routes
  static const String routeDate = '/settings/date';
  static const String routeDateFormat = '/settings/date/format';
  static const String routeTimeDisplay = '/settings/date/time';

  // About Routes
  static const String routeAboutInfo = '/settings/about/info';
  static const String routeUpdate = '/settings/about/update';
  static const String routeTipsTricks = '/settings/about/tips';

  // Notification Routes
  static const String routeNotifications = '/settings/notifications';
  static const String routeAlertThreshold = '/settings/notifications/threshold';
  static const String routeDaysAdvance = '/settings/notifications/days';
  static const String routeNotificationTypes = '/settings/notifications/types';
  static const String routeReminders = '/settings/notifications/reminders';
  static const String routeNotificationUtilities =
      '/settings/notifications/utilities';

  // Debug Routes
  static const String routeDebugNotifications = '/debug/notifications';

  // SharedPreferences keys
  static const String keySetupCompleted = 'setup_completed';
}
