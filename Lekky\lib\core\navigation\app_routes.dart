// File: lib/core/navigation/app_routes.dart
import '../constants/app_constants.dart';

/// Enum representing all possible routes in the app
enum AppRoute {
  // Main Routes
  splash,
  welcome,
  setup,
  home,
  validationDashboard,

  // Main Tab Routes
  history,
  cost,
  mainSettings,

  // Settings Routes
  settings,

  // CSV Routes
  csvExport,
  csvImport,

  // Data Routes
  deleteAllData,

  // About Routes
  about,
  aboutInfo,
  update,
  tipsTricks,

  // Donate Routes
  donate,
  donateOptions,

  // Appearance Routes
  appearance,
  theme,

  // Region Routes
  region,
  language,
  currency,

  // Date Routes
  date,
  dateFormat,
  timeDisplay,

  // Notification Routes
  notifications,
  alertThreshold,
  daysAdvance,
  notificationTypes,
  reminders,
  notificationUtilities,

  // Debug Routes
  debugNotifications;
}

/// Extension to convert AppRoute to path string
extension AppRouteExtension on AppRoute {
  String get path {
    switch (this) {
      case AppRoute.splash:
        return AppConstants.routeSplash;
      case AppRoute.welcome:
        return AppConstants.routeWelcome;
      case AppRoute.setup:
        return AppConstants.routeSetup;
      case AppRoute.home:
        return AppConstants.routeHome;
      case AppRoute.validationDashboard:
        return AppConstants.routeValidationDashboard;
      case AppRoute.history:
        return AppConstants.routeHistory;
      case AppRoute.cost:
        return AppConstants.routeCost;
      case AppRoute.mainSettings:
        return AppConstants.routeMainSettings;
      case AppRoute.settings:
        return AppConstants.routeSettings;
      case AppRoute.csvExport:
        return AppConstants.routeCsvExport;
      case AppRoute.csvImport:
        return AppConstants.routeCsvImport;
      case AppRoute.deleteAllData:
        return AppConstants.routeDeleteAllData;
      case AppRoute.about:
        return AppConstants.routeAbout;
      case AppRoute.aboutInfo:
        return AppConstants.routeAboutInfo;
      case AppRoute.update:
        return AppConstants.routeUpdate;
      case AppRoute.tipsTricks:
        return AppConstants.routeTipsTricks;
      case AppRoute.donate:
        return AppConstants.routeDonate;
      case AppRoute.donateOptions:
        return AppConstants.routeDonateOptions;
      case AppRoute.appearance:
        return AppConstants.routeAppearance;
      case AppRoute.theme:
        return AppConstants.routeTheme;
      case AppRoute.region:
        return AppConstants.routeRegion;
      case AppRoute.language:
        return AppConstants.routeLanguage;
      case AppRoute.currency:
        return AppConstants.routeCurrency;
      case AppRoute.date:
        return AppConstants.routeDate;
      case AppRoute.dateFormat:
        return AppConstants.routeDateFormat;
      case AppRoute.timeDisplay:
        return AppConstants.routeTimeDisplay;
      case AppRoute.notifications:
        return AppConstants.routeNotifications;
      case AppRoute.alertThreshold:
        return AppConstants.routeAlertThreshold;
      case AppRoute.daysAdvance:
        return AppConstants.routeDaysAdvance;
      case AppRoute.notificationTypes:
        return AppConstants.routeNotificationTypes;
      case AppRoute.reminders:
        return AppConstants.routeReminders;
      case AppRoute.notificationUtilities:
        return AppConstants.routeNotificationUtilities;
      case AppRoute.debugNotifications:
        return AppConstants.routeDebugNotifications;
    }
  }

  /// Get route name for go_router
  String get name {
    switch (this) {
      case AppRoute.splash:
        return 'splash';
      case AppRoute.welcome:
        return 'welcome';
      case AppRoute.setup:
        return 'setup';
      case AppRoute.home:
        return 'home';
      case AppRoute.validationDashboard:
        return 'validation-dashboard';
      case AppRoute.history:
        return 'history';
      case AppRoute.cost:
        return 'cost';
      case AppRoute.mainSettings:
        return 'main-settings';
      case AppRoute.settings:
        return 'settings';
      case AppRoute.csvExport:
        return 'csv-export';
      case AppRoute.csvImport:
        return 'csv-import';
      case AppRoute.deleteAllData:
        return 'delete-all-data';
      case AppRoute.about:
        return 'about';
      case AppRoute.aboutInfo:
        return 'about-info';
      case AppRoute.update:
        return 'update';
      case AppRoute.tipsTricks:
        return 'tips-tricks';
      case AppRoute.donate:
        return 'donate';
      case AppRoute.donateOptions:
        return 'donate-options';
      case AppRoute.appearance:
        return 'appearance';
      case AppRoute.theme:
        return 'theme';
      case AppRoute.region:
        return 'region';
      case AppRoute.language:
        return 'language';
      case AppRoute.currency:
        return 'currency';
      case AppRoute.date:
        return 'date';
      case AppRoute.dateFormat:
        return 'date-format';
      case AppRoute.timeDisplay:
        return 'time-display';
      case AppRoute.notifications:
        return 'notifications';
      case AppRoute.alertThreshold:
        return 'alert-threshold';
      case AppRoute.daysAdvance:
        return 'days-advance';
      case AppRoute.notificationTypes:
        return 'notification-types';
      case AppRoute.reminders:
        return 'reminders';
      case AppRoute.notificationUtilities:
        return 'notification-utilities';
      case AppRoute.debugNotifications:
        return 'debug-notifications';
    }
  }
}

/// Route validation utilities
class RouteValidator {
  /// Validate route parameters
  static bool validateRoute(AppRoute route) {
    // All routes are valid for now
    // Add specific validation logic as needed
    return true;
  }
}
