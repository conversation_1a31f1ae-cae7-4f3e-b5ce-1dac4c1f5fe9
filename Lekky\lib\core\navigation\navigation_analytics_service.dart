// File: lib/core/navigation/navigation_analytics_service.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger.dart';
import '../utils/performance_monitor.dart';
import 'app_routes.dart';
import 'navigation_state.dart';

/// Standalone navigation analytics service
class NavigationAnalyticsService {
  final List<NavigationAnalytics> _navigationHistory = [];
  final List<NavigationError> _navigationErrors = [];
  final List<NavigationMetrics> _performanceMetrics = [];
  final Map<String, int> _routeVisitCounts = {};
  final Map<String, Duration> _routeAverageTimes = {};

  /// Track navigation event
  Future<void> trackNavigation(
    AppRoute route, {
    bool isReplacement = false,
    bool isClearAndGo = false,
    bool isDeepLink = false,
  }) async {
    final startTime = DateTime.now();

    try {
      // Update visit counts
      final routeName = route.name;
      _routeVisitCounts[routeName] = (_routeVisitCounts[routeName] ?? 0) + 1;

      // Create analytics entry
      final analytics = NavigationAnalytics(
        routeName: routeName,
        routePath: route.path,
        timestamp: startTime,
        navigationDuration:
            Duration.zero, // Will be updated when navigation completes
        isDeepLink: isDeepLink,
      );

      _navigationHistory.add(analytics);

      // Keep only last 100 entries
      if (_navigationHistory.length > 100) {
        _navigationHistory.removeAt(0);
      }

      Logger.info(
          'Navigation tracked: $routeName (${isReplacement ? 'replacement' : 'push'})');
    } catch (e) {
      Logger.error('Failed to track navigation: $e');
    }
  }

  /// Track back navigation
  void trackBackNavigation() {
    Logger.info('Back navigation tracked');
  }

  /// Track navigation error
  void trackNavigationError(Object error, AppRoute route) {
    try {
      final navigationError = NavigationError(
        message: error.toString(),
        routePath: route.path,
        timestamp: DateTime.now(),
        type: _categorizeError(error),
      );

      _navigationErrors.add(navigationError);

      // Keep only last 50 errors
      if (_navigationErrors.length > 50) {
        _navigationErrors.removeAt(0);
      }

      Logger.error('Navigation error tracked: ${route.path} - $error');
    } catch (e) {
      Logger.error('Failed to track navigation error: $e');
    }
  }

  /// Track navigation performance metrics
  void trackPerformanceMetrics(
    String routeName,
    Duration buildTime,
    Duration transitionTime,
  ) {
    try {
      final totalTime = buildTime + transitionTime;

      final metrics = NavigationMetrics(
        routeName: routeName,
        buildTime: buildTime,
        transitionTime: transitionTime,
        totalTime: totalTime,
        timestamp: DateTime.now(),
      );

      _performanceMetrics.add(metrics);

      // Update average times
      _updateAverageTime(routeName, totalTime);

      // Keep only last 100 metrics
      if (_performanceMetrics.length > 100) {
        _performanceMetrics.removeAt(0);
      }

      Logger.info(
          'Performance metrics tracked: $routeName - ${totalTime.inMilliseconds}ms');
    } catch (e) {
      Logger.error('Failed to track performance metrics: $e');
    }
  }

  /// Get navigation statistics
  Map<String, dynamic> getNavigationStats() {
    return {
      'totalNavigations': _navigationHistory.length,
      'totalErrors': _navigationErrors.length,
      'routeVisitCounts': Map.from(_routeVisitCounts),
      'averageNavigationTimes': Map.from(_routeAverageTimes),
      'mostVisitedRoute': _getMostVisitedRoute(),
      'errorRate': _calculateErrorRate(),
      'averageNavigationTime': _calculateAverageNavigationTime(),
    };
  }

  /// Get recent navigation history
  List<NavigationAnalytics> getRecentNavigations({int limit = 20}) {
    final recentNavigations = _navigationHistory.reversed.take(limit).toList();
    return recentNavigations.reversed.toList();
  }

  /// Get recent navigation errors
  List<NavigationError> getRecentErrors({int limit = 10}) {
    final recentErrors = _navigationErrors.reversed.take(limit).toList();
    return recentErrors.reversed.toList();
  }

  /// Get performance metrics for a route
  List<NavigationMetrics> getRouteMetrics(String routeName) {
    return _performanceMetrics
        .where((metric) => metric.routeName == routeName)
        .toList();
  }

  /// Clear all analytics data
  void clearAnalytics() {
    _navigationHistory.clear();
    _navigationErrors.clear();
    _performanceMetrics.clear();
    _routeVisitCounts.clear();
    _routeAverageTimes.clear();
    Logger.info('Navigation analytics cleared');
  }

  /// Categorize navigation error
  NavigationErrorType _categorizeError(Object error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('not found') || errorString.contains('404')) {
      return NavigationErrorType.routeNotFound;
    } else if (errorString.contains('parameter') ||
        errorString.contains('invalid')) {
      return NavigationErrorType.invalidParameters;
    } else if (errorString.contains('permission')) {
      return NavigationErrorType.permissionDenied;
    } else if (errorString.contains('network') ||
        errorString.contains('connection')) {
      return NavigationErrorType.networkError;
    } else {
      return NavigationErrorType.unknown;
    }
  }

  /// Update average time for a route
  void _updateAverageTime(String routeName, Duration newTime) {
    final currentAverage = _routeAverageTimes[routeName] ?? Duration.zero;
    final visitCount = _routeVisitCounts[routeName] ?? 1;

    // Calculate new average
    final totalMs = (currentAverage.inMilliseconds * (visitCount - 1)) +
        newTime.inMilliseconds;
    final newAverageMs = totalMs ~/ visitCount;

    _routeAverageTimes[routeName] = Duration(milliseconds: newAverageMs);
  }

  /// Get most visited route
  String? _getMostVisitedRoute() {
    if (_routeVisitCounts.isEmpty) return null;

    return _routeVisitCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Calculate error rate
  double _calculateErrorRate() {
    final totalNavigations = _navigationHistory.length;
    if (totalNavigations == 0) return 0.0;

    return (_navigationErrors.length / totalNavigations) * 100;
  }

  /// Calculate average navigation time
  Duration _calculateAverageNavigationTime() {
    if (_performanceMetrics.isEmpty) return Duration.zero;

    final totalMs = _performanceMetrics
        .map((m) => m.totalTime.inMilliseconds)
        .reduce((a, b) => a + b);

    return Duration(milliseconds: totalMs ~/ _performanceMetrics.length);
  }
}

/// Navigation analytics service provider
final navigationAnalyticsServiceProvider =
    Provider<NavigationAnalyticsService>((ref) {
  return NavigationAnalyticsService();
});
