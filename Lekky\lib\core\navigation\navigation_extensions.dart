// File: lib/core/navigation/navigation_extensions.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app_routes.dart';
import 'navigation_service.dart';

/// Enhanced navigation extensions using the centralized navigation service
extension NavigationExtensions on BuildContext {
  /// Get navigation service from Riverpod
  NavigationService get _navigationService =>
      ProviderScope.containerOf(this).read(navigationServiceProvider);

  // Main Routes
  Future<T?> navigateToSplash<T>() =>
      _navigationService.navigateTo<T>(AppRoute.splash);
  Future<T?> navigateToWelcome<T>() =>
      _navigationService.navigateTo<T>(AppRoute.welcome);
  Future<T?> navigateToSetup<T>() =>
      _navigationService.navigateTo<T>(AppRoute.setup);
  Future<T?> navigateToHome<T>() =>
      _navigationService.navigateTo<T>(AppRoute.home);
  Future<T?> navigateToValidationDashboard<T>() =>
      _navigationService.navigateTo<T>(AppRoute.validationDashboard);

  // Main Tab Routes
  Future<T?> navigateToHistory<T>() =>
      _navigationService.navigateTo<T>(AppRoute.history);
  Future<T?> navigateToCost<T>() =>
      _navigationService.navigateTo<T>(AppRoute.cost);
  Future<T?> navigateToMainSettings<T>() =>
      _navigationService.navigateTo<T>(AppRoute.mainSettings);

  // Settings Routes
  Future<T?> navigateToSettings<T>({int? expanded}) =>
      _navigationService.navigateTo<T>(AppRoute.settings);

  // CSV Routes
  Future<T?> navigateToCsvExport<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.csvExport());
  Future<T?> navigateToCsvImport<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.csvImport());

  // Data Routes
  Future<T?> navigateToDeleteAllData<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.deleteAllData());

  // About Routes
  Future<T?> navigateToAbout<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.about());
  Future<T?> navigateToAboutInfo<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.aboutInfo());
  Future<T?> navigateToUpdate<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.update());
  Future<T?> navigateToTipsTricks<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.tipsTricks());

  // Donate Routes
  Future<T?> navigateToDonate<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.donate());
  Future<T?> navigateToDonateOptions<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.donateOptions());

  // Appearance Routes
  Future<T?> navigateToAppearance<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.appearance());
  Future<T?> navigateToTheme<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.theme());

  // Region Routes
  Future<T?> navigateToRegion<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.region());
  Future<T?> navigateToLanguage<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.language());
  Future<T?> navigateToCurrency<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.currency());

  // Date Routes
  Future<T?> navigateToDate<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.date());
  Future<T?> navigateToDateFormat<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.dateFormat());
  Future<T?> navigateToTimeDisplay<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.timeDisplay());

  // Notification Routes
  Future<T?> navigateToNotifications<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.notifications());
  Future<T?> navigateToAlertThreshold<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.alertThreshold());
  Future<T?> navigateToDaysAdvance<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.daysAdvance());
  Future<T?> navigateToNotificationTypes<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.notificationTypes());
  Future<T?> navigateToReminders<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.reminders());
  Future<T?> navigateToNotificationUtilities<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.notificationUtilities());

  // Debug Routes
  Future<T?> navigateToDebugNotifications<T>() =>
      _navigationService.navigateTo<T>(const AppRoute.debugNotifications());

  // Declarative Navigation (go)
  void goToSplash() => _navigationService.goTo(const AppRoute.splash());
  void goToWelcome() => _navigationService.goTo(const AppRoute.welcome());
  void goToSetup() => _navigationService.goTo(const AppRoute.setup());
  void goToHome() => _navigationService.goTo(const AppRoute.home());
  void goToValidationDashboard() =>
      _navigationService.goTo(const AppRoute.validationDashboard());

  void goToHistory() => _navigationService.goTo(const AppRoute.history());
  void goToCost() => _navigationService.goTo(const AppRoute.cost());
  void goToMainSettings() =>
      _navigationService.goTo(const AppRoute.mainSettings());

  void goToSettings({int? expanded}) =>
      _navigationService.goTo(AppRoute.settings(expanded: expanded));

  void goToCsvExport() => _navigationService.goTo(const AppRoute.csvExport());
  void goToCsvImport() => _navigationService.goTo(const AppRoute.csvImport());
  void goToDeleteAllData() =>
      _navigationService.goTo(const AppRoute.deleteAllData());

  void goToAbout() => _navigationService.goTo(const AppRoute.about());
  void goToAboutInfo() => _navigationService.goTo(const AppRoute.aboutInfo());
  void goToUpdate() => _navigationService.goTo(const AppRoute.update());
  void goToTipsTricks() => _navigationService.goTo(const AppRoute.tipsTricks());

  void goToDonate() => _navigationService.goTo(const AppRoute.donate());
  void goToDonateOptions() =>
      _navigationService.goTo(const AppRoute.donateOptions());

  void goToAppearance() => _navigationService.goTo(const AppRoute.appearance());
  void goToTheme() => _navigationService.goTo(const AppRoute.theme());

  void goToRegion() => _navigationService.goTo(const AppRoute.region());
  void goToLanguage() => _navigationService.goTo(const AppRoute.language());
  void goToCurrency() => _navigationService.goTo(const AppRoute.currency());

  void goToDate() => _navigationService.goTo(const AppRoute.date());
  void goToDateFormat() => _navigationService.goTo(const AppRoute.dateFormat());
  void goToTimeDisplay() =>
      _navigationService.goTo(const AppRoute.timeDisplay());

  void goToNotifications() =>
      _navigationService.goTo(const AppRoute.notifications());
  void goToAlertThreshold() =>
      _navigationService.goTo(const AppRoute.alertThreshold());
  void goToDaysAdvance() =>
      _navigationService.goTo(const AppRoute.daysAdvance());
  void goToNotificationTypes() =>
      _navigationService.goTo(const AppRoute.notificationTypes());
  void goToReminders() => _navigationService.goTo(const AppRoute.reminders());
  void goToNotificationUtilities() =>
      _navigationService.goTo(const AppRoute.notificationUtilities());

  void goToDebugNotifications() =>
      _navigationService.goTo(const AppRoute.debugNotifications());

  // Replacement Navigation
  Future<T?> replaceWithHome<T>() =>
      _navigationService.navigateToReplacement<T>(const AppRoute.home());
  Future<T?> replaceWithHistory<T>() =>
      _navigationService.navigateToReplacement<T>(const AppRoute.history());
  Future<T?> replaceWithSettings<T>({int? expanded}) => _navigationService
      .navigateToReplacement<T>(AppRoute.settings(expanded: expanded));

  // Back Navigation
  void navigateBack() => _navigationService.goBack();
  void navigateBackWithResult<T>(T result) =>
      _navigationService.goBackWithResult<T>(result);
  bool canNavigateBack() => _navigationService.canGoBack();

  // Clear and Go Navigation
  void clearAndGoToHome() =>
      _navigationService.clearAndGoTo(const AppRoute.home());
  void clearAndGoToSettings({int? expanded}) =>
      _navigationService.clearAndGoTo(AppRoute.settings(expanded: expanded));

  // Current Location
  String get currentLocation => _navigationService.currentLocation;
}
