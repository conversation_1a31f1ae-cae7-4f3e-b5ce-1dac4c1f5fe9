// File: lib/core/navigation/navigation_state.dart
import 'app_routes.dart';

/// Navigation state model
class NavigationState {
  final AppRoute currentRoute;
  final List<AppRoute> routeHistory;
  final Map<String, dynamic> routeParameters;
  final bool isNavigating;
  final String? pendingRoute;
  final String? lastError;
  final int navigationCount;
  final DateTime lastNavigationTime;

  const NavigationState({
    required this.currentRoute,
    required this.routeHistory,
    this.routeParameters = const {},
    this.isNavigating = false,
    this.pendingRoute,
    this.lastError,
    this.navigationCount = 0,
    required this.lastNavigationTime,
  });

  /// Create initial navigation state
  factory NavigationState.initial() {
    return NavigationState(
      currentRoute: const AppRoute.splash(),
      routeHistory: const [AppRoute.splash()],
      lastNavigationTime: DateTime.now(),
    );
  }

  /// Copy with method
  NavigationState copyWith({
    AppRoute? currentRoute,
    List<AppRoute>? routeHistory,
    Map<String, dynamic>? routeParameters,
    bool? isNavigating,
    String? pendingRoute,
    String? lastError,
    int? navigationCount,
    DateTime? lastNavigationTime,
  }) {
    return NavigationState(
      currentRoute: currentRoute ?? this.currentRoute,
      routeHistory: routeHistory ?? this.routeHistory,
      routeParameters: routeParameters ?? this.routeParameters,
      isNavigating: isNavigating ?? this.isNavigating,
      pendingRoute: pendingRoute ?? this.pendingRoute,
      lastError: lastError ?? this.lastError,
      navigationCount: navigationCount ?? this.navigationCount,
      lastNavigationTime: lastNavigationTime ?? this.lastNavigationTime,
    );
  }
}

/// Navigation analytics data
class NavigationAnalytics {
  final String routeName;
  final String routePath;
  final DateTime timestamp;
  final Duration navigationDuration;
  final Map<String, dynamic> parameters;
  final String? previousRoute;
  final String? error;
  final bool isDeepLink;

  const NavigationAnalytics({
    required this.routeName,
    required this.routePath,
    required this.timestamp,
    required this.navigationDuration,
    this.parameters = const {},
    this.previousRoute,
    this.error,
    this.isDeepLink = false,
  });
}

/// Navigation error model
class NavigationError {
  final String message;
  final String routePath;
  final DateTime timestamp;
  final String? stackTrace;
  final Map<String, dynamic> context;
  final NavigationErrorType type;

  const NavigationError({
    required this.message,
    required this.routePath,
    required this.timestamp,
    this.stackTrace,
    this.context = const {},
    required this.type,
  });
}

/// Navigation error types
enum NavigationErrorType {
  routeNotFound,
  invalidParameters,
  navigationFailed,
  permissionDenied,
  networkError,
  unknown,
}

/// Navigation performance metrics
class NavigationMetrics {
  final String routeName;
  final Duration buildTime;
  final Duration transitionTime;
  final Duration totalTime;
  final DateTime timestamp;
  final int memoryUsage;
  final bool wasPreloaded;

  const NavigationMetrics({
    required this.routeName,
    required this.buildTime,
    required this.transitionTime,
    required this.totalTime,
    required this.timestamp,
    this.memoryUsage = 0,
    this.wasPreloaded = false,
  });
}
