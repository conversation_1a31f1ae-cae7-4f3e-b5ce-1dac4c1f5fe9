import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../../features/splash/presentation/screens/simplified_splash_screen.dart';
import '../../features/welcome/presentation/screens/welcome_screen.dart';
import '../../features/setup/presentation/screens/setup_screen.dart';
import '../../features/home/<USER>/screens/riverpod_dashboard_screen.dart';
import '../../features/history/presentation/screens/riverpod_history_screen.dart';
import '../../features/cost/presentation/screens/cost_screen.dart';
import '../../features/settings/presentation/screens/settings_screen.dart';
import '../../features/settings/presentation/screens/csv_export_screen.dart';
import '../../features/settings/presentation/screens/csv_import_screen.dart';
import '../../features/settings/presentation/screens/about_screen.dart';
import '../../features/settings/presentation/screens/donate_screen.dart';
import '../../features/settings/presentation/screens/appearance_screen.dart';
import '../../features/settings/presentation/screens/region_screen.dart';
import '../../features/settings/presentation/screens/language_screen.dart';
import '../../features/settings/presentation/screens/currency_screen.dart';
import '../../features/settings/presentation/screens/date_screen.dart';
import '../../features/settings/presentation/screens/update_screen.dart';
import '../../features/settings/presentation/screens/alert_threshold_screen.dart';
import '../../features/settings/presentation/screens/days_advance_screen.dart';
import '../../features/settings/presentation/screens/notification_types_screen.dart';
import '../../features/settings/presentation/screens/reminders_screen.dart';
import '../../features/settings/presentation/screens/notification_utilities_screen.dart';
import '../../features/settings/presentation/screens/tips_tricks_screen.dart';
import '../../features/settings/presentation/screens/delete_all_data_screen.dart';
import '../../features/settings/presentation/screens/date_format_screen.dart';
import '../../features/settings/presentation/screens/time_display_screen.dart';
import '../../features/settings/presentation/screens/theme_mode_screen.dart';
import '../../features/validation/presentation/screens/riverpod_validation_dashboard_screen.dart';
import '../../features/notifications/presentation/screens/notification_debug_screen.dart';
import 'main_shell_route.dart';

/// Router configuration for the Lekky app using go_router
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppConstants.routeSplash,
    debugLogDiagnostics: true,
    routes: [
      // Splash Screen
      GoRoute(
        path: AppConstants.routeSplash,
        name: 'splash',
        builder: (context, state) => const SimplifiedSplashScreen(),
      ),

      // Welcome Screen
      GoRoute(
        path: AppConstants.routeWelcome,
        name: 'welcome',
        builder: (context, state) => const WelcomeScreen(),
      ),

      // Setup Screen
      GoRoute(
        path: AppConstants.routeSetup,
        name: 'setup',
        builder: (context, state) => const SetupScreen(),
      ),

      // Main Shell Route with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainShellRoute(child: child),
        routes: [
          // Home Tab
          GoRoute(
            path: AppConstants.routeHome,
            name: 'home',
            builder: (context, state) => const RiverpodDashboardScreen(),
          ),

          // History Tab
          GoRoute(
            path: AppConstants.routeHistory,
            name: 'history',
            builder: (context, state) => const RiverpodHistoryScreen(),
          ),

          // Cost Tab
          GoRoute(
            path: AppConstants.routeCost,
            name: 'cost',
            builder: (context, state) => const CostScreen(),
          ),

          // Settings Tab
          GoRoute(
            path: AppConstants.routeMainSettings,
            name: 'main-settings',
            builder: (context, state) => const SettingsScreen(),
          ),
        ],
      ),

      // Standalone Settings Routes
      GoRoute(
        path: AppConstants.routeSettings,
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
        routes: [
          // CSV Routes
          GoRoute(
            path: 'csv/export',
            name: 'csv-export',
            builder: (context, state) => const CsvExportScreen(),
          ),
          GoRoute(
            path: 'csv/import',
            name: 'csv-import',
            builder: (context, state) => const CsvImportScreen(),
          ),

          // Data Routes
          GoRoute(
            path: 'data/delete-all',
            name: 'delete-all-data',
            builder: (context, state) => const DeleteAllDataScreen(),
          ),

          // About Routes
          GoRoute(
            path: 'about',
            name: 'about',
            builder: (context, state) => const AboutScreen(),
            routes: [
              GoRoute(
                path: 'info',
                name: 'about-info',
                builder: (context, state) => const AboutScreen(),
              ),
              GoRoute(
                path: 'update',
                name: 'about-update',
                builder: (context, state) => const UpdateScreen(),
              ),
              GoRoute(
                path: 'tips',
                name: 'about-tips',
                builder: (context, state) => const TipsTricksScreen(),
              ),
            ],
          ),

          // Donate Routes
          GoRoute(
            path: 'donate',
            name: 'donate',
            builder: (context, state) => const DonateScreen(),
            routes: [
              GoRoute(
                path: 'options',
                name: 'donate-options',
                builder: (context, state) => const DonateScreen(),
              ),
            ],
          ),

          // Appearance Routes
          GoRoute(
            path: 'appearance',
            name: 'appearance',
            builder: (context, state) => const AppearanceScreen(),
            routes: [
              GoRoute(
                path: 'theme',
                name: 'appearance-theme',
                builder: (context, state) => const ThemeModeScreen(),
              ),
            ],
          ),

          // Region Routes
          GoRoute(
            path: 'region',
            name: 'region',
            builder: (context, state) => const RegionScreen(),
            routes: [
              GoRoute(
                path: 'language',
                name: 'region-language',
                builder: (context, state) => const LanguageScreen(),
              ),
              GoRoute(
                path: 'currency',
                name: 'region-currency',
                builder: (context, state) => const CurrencyScreen(),
              ),
            ],
          ),

          // Date Routes
          GoRoute(
            path: 'date',
            name: 'date',
            builder: (context, state) => const DateScreen(),
            routes: [
              GoRoute(
                path: 'format',
                name: 'date-format',
                builder: (context, state) => const DateFormatScreen(),
              ),
              GoRoute(
                path: 'time',
                name: 'date-time',
                builder: (context, state) => const TimeDisplayScreen(),
              ),
            ],
          ),

          // Notification Routes
          GoRoute(
            path: 'notifications',
            name: 'notifications',
            builder: (context, state) =>
                const SettingsScreen(), // Redirect to main settings
            routes: [
              GoRoute(
                path: 'threshold',
                name: 'notifications-threshold',
                builder: (context, state) => const AlertThresholdScreen(),
              ),
              GoRoute(
                path: 'days',
                name: 'notifications-days',
                builder: (context, state) => const DaysAdvanceScreen(),
              ),
              GoRoute(
                path: 'types',
                name: 'notifications-types',
                builder: (context, state) => const NotificationTypesScreen(),
              ),
              GoRoute(
                path: 'reminders',
                name: 'notifications-reminders',
                builder: (context, state) => const RemindersScreen(),
              ),
              GoRoute(
                path: 'utilities',
                name: 'notifications-utilities',
                builder: (context, state) =>
                    const NotificationUtilitiesScreen(),
              ),
            ],
          ),
        ],
      ),

      // Validation Dashboard
      GoRoute(
        path: AppConstants.routeValidationDashboard,
        name: 'validation-dashboard',
        builder: (context, state) => const RiverpodValidationDashboardScreen(),
      ),

      // Debug Routes
      GoRoute(
        path: AppConstants.routeDebugNotifications,
        name: 'debug-notifications',
        builder: (context, state) => const NotificationDebugScreen(),
      ),
    ],
  );
});

/// Extension for easy navigation using go_router
extension AppRouterExtension on BuildContext {
  /// Navigate to home tab
  void goToHome() => go(AppConstants.routeHome);

  /// Navigate to history tab
  void goToHistory() => go(AppConstants.routeHistory);

  /// Navigate to cost tab
  void goToCost() => go(AppConstants.routeCost);

  /// Navigate to main settings tab
  void goToMainSettings() => go(AppConstants.routeMainSettings);

  /// Navigate to standalone settings
  void goToSettings() => go(AppConstants.routeSettings);

  /// Navigate to validation dashboard
  void goToValidationDashboard() => go(AppConstants.routeValidationDashboard);
}
