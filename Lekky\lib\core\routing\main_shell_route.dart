import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_constants.dart';
import '../providers/settings_provider.dart';
import '../constants/currency_constants.dart';
import '../theme/app_colors.dart';
import '../localization/app_localizations.dart';

/// Shell route widget that provides the main app structure with bottom navigation
class MainShellRoute extends ConsumerStatefulWidget {
  /// The child widget to display in the body
  final Widget child;

  /// Constructor
  const MainShellRoute({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<MainShellRoute> createState() => _MainShellRouteState();
}

class _MainShellRouteState extends ConsumerState<MainShellRoute> {
  int _selectedIndex = 0;

  /// Get the current tab index based on the current route
  int _getCurrentIndex(String location) {
    if (location.startsWith(AppConstants.routeHome)) return 0;
    if (location.startsWith(AppConstants.routeHistory)) return 1;
    if (location.startsWith(AppConstants.routeCost)) return 2;
    if (location.startsWith(AppConstants.routeMainSettings)) return 3;
    return 0; // Default to home
  }

  /// Handle tab selection
  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        context.go(AppConstants.routeHome);
        break;
      case 1:
        context.go(AppConstants.routeHistory);
        break;
      case 2:
        context.go(AppConstants.routeCost);
        break;
      case 3:
        context.go(AppConstants.routeMainSettings);
        break;
    }
  }

  /// Get the selected navigation color based on current tab and theme
  Color _getSelectedNavColor(int selectedIndex, bool isDark) {
    switch (selectedIndex) {
      case 0:
        return isDark ? AppColors.homeAppBarDark : AppColors.homeAppBarLight;
      case 1:
        return isDark
            ? AppColors.historyAppBarDark
            : AppColors.historyAppBarLight;
      case 2:
        return isDark ? AppColors.costAppBarDark : AppColors.costAppBarLight;
      case 3:
        return isDark
            ? AppColors.settingsAppBarDark
            : AppColors.settingsAppBarLight;
      default:
        return isDark ? AppColors.primaryDark : AppColors.primary;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Update selected index based on current location
    final location = GoRouterState.of(context).location;
    _selectedIndex = _getCurrentIndex(location);

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: Consumer(
        builder: (context, ref, _) {
          final settingsAsync = ref.watch(settingsProvider);

          return settingsAsync.when(
            loading: () => BottomNavigationBar(
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.home),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.history),
                  label: 'History',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.attach_money),
                  label: 'Cost',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.settings),
                  label: 'Settings',
                ),
              ],
              currentIndex: _selectedIndex,
              onTap: _onItemTapped,
              type: BottomNavigationBarType.fixed,
            ),
            error: (error, stackTrace) => BottomNavigationBar(
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.home),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.history),
                  label: 'History',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.attach_money),
                  label: 'Cost',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.settings),
                  label: 'Settings',
                ),
              ],
              currentIndex: _selectedIndex,
              onTap: _onItemTapped,
              type: BottomNavigationBarType.fixed,
            ),
            data: (settings) {
              final costIcon =
                  RegionalConstants.getCurrencyIcon(settings.currency) ??
                      Icons.attach_money;
              final isDark = Theme.of(context).brightness == Brightness.dark;

              return BottomNavigationBar(
                items: [
                  const BottomNavigationBarItem(
                    icon: Icon(Icons.home),
                    label: 'Home',
                  ),
                  const BottomNavigationBarItem(
                    icon: Icon(Icons.history),
                    label: 'History',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(costIcon),
                    label: 'Cost',
                  ),
                  const BottomNavigationBarItem(
                    icon: Icon(Icons.settings),
                    label: 'Settings',
                  ),
                ],
                currentIndex: _selectedIndex,
                selectedItemColor: _getSelectedNavColor(_selectedIndex, isDark),
                unselectedItemColor: Theme.of(context)
                    .bottomNavigationBarTheme
                    .unselectedItemColor,
                onTap: _onItemTapped,
                type: BottomNavigationBarType.fixed,
              );
            },
          );
        },
      ),
    );
  }
}
