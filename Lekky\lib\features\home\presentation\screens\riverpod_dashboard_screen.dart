import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/dashboard_provider.dart';
import '../widgets/meter_status_card.dart';
import '../widgets/usage_statistics_card.dart';
import '../widgets/top_up_statistics_card.dart';
import '../widgets/quick_actions_card.dart';
import '../widgets/recent_activity_card.dart';
import '../../domain/models/dashboard_state.dart';
import '../../../../core/providers/preference_provider.dart';
import '../../../../core/widgets/message_banner.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/routing/app_router.dart';
import '../../../entries/presentation/dialogs/riverpod_add_entry_dialog.dart';
import '../../../validation/presentation/dialogs/validation_dialog.dart';
import '../../../validation/presentation/widgets/validation_icon_widget.dart';
import '../../../notifications/presentation/widgets/notification_badge.dart';
import '../../../notifications/presentation/providers/notification_provider.dart';
import '../../../notifications/presentation/dialogs/notification_dialog.dart';

/// Riverpod-based Dashboard screen demonstrating the migration pattern
/// This shows how to use the new DashboardProvider with reactive data updates
class RiverpodDashboardScreen extends ConsumerStatefulWidget {
  const RiverpodDashboardScreen({super.key});

  @override
  ConsumerState<RiverpodDashboardScreen> createState() =>
      _RiverpodDashboardScreenState();
}

class _RiverpodDashboardScreenState
    extends ConsumerState<RiverpodDashboardScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final dashboardAsync = ref.watch(dashboardProvider);
    final notificationAsync = ref.watch(notificationProvider);

    return Scaffold(
      body: dashboardAsync.when(
        data: (dashboardState) => _buildDashboardContent(
          context,
          dashboardState,
          notificationAsync,
        ),
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Failed to load dashboard',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(dashboardProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the main dashboard content
  Widget _buildDashboardContent(
    BuildContext context,
    DashboardState dashboardState,
    AsyncValue<dynamic> notificationAsync,
  ) {
    if (dashboardState.hasError) {
      return Column(
        children: [
          // Error banner
          AppBanner(
            message: dashboardState.errorMessage!,
            backgroundColor: AppColors.homeAppBarLight,
            onDismiss: () {
              ref.read(dashboardProvider.notifier).clearError();
              ref.read(dashboardProvider.notifier).refresh();
            },
          ),
          const Expanded(
            child: Center(
              child: Text('Please try refreshing the dashboard'),
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        // App Banner with notification badge overlay
        LayoutBuilder(
          builder: (context, constraints) {
            // Calculate notification icon position to align with Dashboard text
            final mediaQuery = MediaQuery.of(context);
            final safeAreaTop = mediaQuery.padding.top;
            const bannerVerticalPadding = 12.0;
            const textHeight = 28.0;
            const textLineHeight = 1.2;
            const iconSize = 28.0;

            // Calculate text center position
            final textCenterY = safeAreaTop +
                bannerVerticalPadding +
                (textHeight * textLineHeight) / 2;
            // Position icon center to align with text center
            final iconTopPosition = textCenterY - (iconSize / 2);

            return Stack(
              children: [
                AppBanner(
                  message: 'Dashboard',
                  gradientColors: AppColors.getDashboardMainCardGradient(
                      Theme.of(context).brightness == Brightness.dark),
                  textColor: AppColors.getAppBarTextColor(
                      'home', Theme.of(context).brightness == Brightness.dark),
                ),
                // Entry Validation icon positioned left of notification badge
                Positioned(
                  top: iconTopPosition,
                  right:
                      68, // 16 (banner padding) + 28 (notification icon) + 24 (spacing)
                  child: ValidationIconWidget(
                    onTap: () => showValidationDialog(context),
                  ),
                ),
                // Notification badge positioned to align with Dashboard text
                Positioned(
                  top: iconTopPosition,
                  right: 16,
                  child: notificationAsync.when(
                    data: (notificationState) => NotificationBadge(
                      count: notificationState.unreadCount,
                      onTap: () => _showNotifications(),
                    ),
                    loading: () => const SizedBox.shrink(),
                    error: (_, __) => const SizedBox.shrink(),
                  ),
                ),
              ],
            );
          },
        ),

        // Tip banner with theme-based colors and automatic cycling
        MessageBanner(
          messages: _getTips(),
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF1E1E1E) // Same as settings cards dark mode
              : AppColors.tipBannerLight,
        ),

        // Main content
        Expanded(
          child: RefreshIndicator(
            onRefresh: () => ref.read(dashboardProvider.notifier).refresh(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16.0),
              child: _buildDashboardCards(context, dashboardState),
            ),
          ),
        ),
      ],
    );
  }

  /// Build dashboard cards
  Widget _buildDashboardCards(
      BuildContext context, DashboardState dashboardState) {
    final preferencesAsync = ref.watch(preferencesProvider);

    return preferencesAsync.when(
      data: (preferences) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Meter Status Card
          MeterStatusCard(
            meterValue: dashboardState.latestMeterReading?.value,
            meterDate: dashboardState.latestMeterReading?.date,
            daysRemaining: dashboardState.calculateDaysRemaining(),
            currencySymbol: preferences.currencySymbol,
            totalTopUpsAfterLatestReading:
                dashboardState.totalTopUpsAfterLatestReading,
            onRefresh: () => ref.read(dashboardProvider.notifier).refresh(),
          ),
          const SizedBox(height: 16),

          // Quick Actions Card
          QuickActionsCard(
            onAddEntry: () => _showAddEntryDialog(preferences.currencySymbol),
          ),
          const SizedBox(height: 16),

          // Usage Statistics Card
          UsageStatisticsCard(
            recentAverageDailyUsage: dashboardState.recentAverageDailyUsage,
            totalAverageDailyUsage: dashboardState.totalAverageDailyUsage,
            currencySymbol: preferences.currencySymbol,
          ),
          const SizedBox(height: 16),

          // Top Up Statistics Card
          TopUpStatisticsCard(
            daysToAlertThreshold: dashboardState.calculateDaysToAlertThreshold(
              preferences.alertThreshold,
              preferences.daysInAdvance,
            ),
            daysToMeterZero: dashboardState.calculateDaysToMeterZero(),
            currencySymbol: preferences.currencySymbol,
            alertThreshold: preferences.alertThreshold,
            daysInAdvance: preferences.daysInAdvance,
            dateFormat: preferences.dateFormat,
          ),
          const SizedBox(height: 16),

          // Recent Activity Card
          RecentActivityCard(
            recentEntries: dashboardState.recentEntries,
            currencySymbol: preferences.currencySymbol,
            onViewAll: _navigateToHistory,
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(
        child: Text('Error loading preferences: $error'),
      ),
    );
  }

  /// Show dialog to add a new entry
  void _showAddEntryDialog(String currencySymbol) {
    showDialog(
      context: context,
      builder: (context) => RiverpodAddEntryDialog(
        currencySymbol: currencySymbol,
        onEntryAdded: () {
          // Refresh the dashboard when an entry is added
          ref.read(dashboardProvider.notifier).refresh();
        },
      ),
    );
  }

  /// Navigate to the History screen
  void _navigateToHistory() {
    if (context.mounted) {
      context.goToHistory();
    }
  }

  /// Show the Notifications dialog
  void _showNotifications() {
    if (context.mounted) {
      showNotificationDialog(context);
    }
  }

  /// Get the list of tip messages
  List<String> _getTips() {
    return [
      'Recent-avg shows usage between consecutive readings',
      'Tap the notification bell icon to view all notifications',
      'Add new meter readings regularly for better usage statistics',
      'Set up alerts to be notified when your balance is low',
      'Use the Quick Actions to add new readings or top-ups',
      'View your history to see all past meter readings and top-ups',
      'Notifications are grouped by type for easy organization',
      'Swipe left on notifications to mark as read, right to delete',
      'Configure notification thresholds in Settings > Alerts & Notifications',
      'Low balance alerts help you avoid running out of credit',
      'Set "Days in Advance" to get top-up reminders early',
      'History filters work together - combine entry type, sort order, and date range',
      'Android 13+ requires notification permission for alerts to work properly',
      'Check device settings if notifications stop working',
    ];
  }
}
