import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/export_data_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/providers/progress_provider.dart';

/// CSV Export screen for exporting data to CSV format
class CsvExportScreen extends ConsumerStatefulWidget {
  /// Constructor
  const CsvExportScreen({super.key});

  @override
  ConsumerState<CsvExportScreen> createState() => _CsvExportScreenState();
}

class _CsvExportScreenState extends ConsumerState<CsvExportScreen> {
  @override
  Widget build(BuildContext context) {
    final exportState = ref.watch(exportDataProvider);
    final progressState = ref.watch(exportProgressProvider);

    return PopScope(
      canPop: !exportState.isExporting,
      onPopInvoked: (didPop) async {
        if (didPop) return;

        final shouldLeave = await showDialog<bool>(
          context: context,
          builder: (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.0),
            ),
            elevation: 24.0,
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.orange),
                      const SizedBox(width: 8.0),
                      const Text(
                        'Export in Progress',
                        style: TextStyle(
                          fontSize: 20.0,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(false),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Leaving now may interrupt the export process. Are you sure you want to continue?',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Stay'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Leave'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );

        if (shouldLeave == true && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: Column(
          children: [
            // Banner with back arrow
            GestureDetector(
              onTap: exportState.isExporting
                  ? null
                  : () => Navigator.of(context).pop(),
              child: AppBanner(
                message: '← Export Data',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Show progress card during export, otherwise show export information card
                    progressState.isActive
                        ? _buildProgressCard(progressState)
                        : _buildExportInformationCard(),

                    const SizedBox(height: 24),

                    // Export Button or Done Button
                    SizedBox(
                      width: double.infinity,
                      child: exportState.isCompleted
                          ? ElevatedButton(
                              onPressed: () => _navigateBackToSettings(),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                              ),
                              child: const Text('Done'),
                            )
                          : ElevatedButton.icon(
                              onPressed: exportState.isExporting
                                  ? null
                                  : () => _performExport(),
                              icon: exportState.isExporting
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    )
                                  : const Icon(Icons.file_download),
                              label: Text(exportState.isExporting
                                  ? 'Exporting...'
                                  : 'Export Data'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                disabledBackgroundColor: Colors.grey,
                              ),
                            ),
                    ),

                    const SizedBox(height: 16),

                    // Help Text
                    const Text(
                      'The exported file will be compatible with the Lekky import format and can be used to restore your data or transfer it to another device.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Perform the CSV export operation
  Future<void> _performExport() async {
    // Use the Riverpod provider to export data
    await ref.read(exportDataProvider.notifier).exportData();

    // Show appropriate message based on result
    if (mounted) {
      final exportState = ref.read(exportDataProvider);
      if (exportState.isSuccessful) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data exported successfully to Downloads folder'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Export failed: ${exportState.errorMessage ?? 'Unknown error'}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Navigate back to settings
  Future<void> _navigateBackToSettings() async {
    if (mounted) {
      // Reset the export state before navigating
      ref.read(exportDataProvider.notifier).resetState();
      context.go('/settings');
    }
  }

  /// Build the export information card
  Widget _buildExportInformationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.file_download, color: Colors.blue),
                SizedBox(width: 16),
                Text(
                  'Export to CSV',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Export all your meter readings and top-ups to a CSV file. The file will be saved to your Downloads folder and can be imported later or used with other applications.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            const Text(
              'Export Format:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '# Lekky v1.0.1 BackupFormat=101',
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                      color: Colors.green,
                    ),
                  ),
                  Text(
                    'Date,Type,Amount',
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '2024-11-24T20:52:50.000,0,7.35',
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '2024-11-24T20:53:21.000,1,150.0',
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Type: 0 = Meter Reading, 1 = Top-up',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the progress card (following import screen pattern)
  Widget _buildProgressCard(ProgressState progressState) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Export Progress',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: progressState.progress,
            backgroundColor: Colors.grey[200],
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          const SizedBox(height: 8),
          Text(
            progressState.statusMessage,
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
          _buildStepIndicator(progressState),
        ],
      ),
    );
  }

  /// Build the step indicator (adapted from import screen)
  Widget _buildStepIndicator(ProgressState progressState) {
    final steps = ['Prepare', 'Generate', 'Save', 'Verify'];
    final currentStepIndex = _getCurrentStepIndex(progressState.statusMessage);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        for (int i = 0; i < steps.length; i++) ...[
          _buildStepCircle(i, steps[i], currentStepIndex),
          if (i < steps.length - 1) _buildStepDivider(i, currentStepIndex),
        ],
      ],
    );
  }

  /// Build a step circle
  Widget _buildStepCircle(int stepIndex, String label, int currentStepIndex) {
    final isActive = currentStepIndex >= stepIndex;
    final isComplete = currentStepIndex > stepIndex;

    return Column(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? AppColors.primary : Colors.grey[300],
          ),
          child: isComplete
              ? const Icon(Icons.check, color: Colors.white, size: 16)
              : null,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: isActive ? AppColors.primary : Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// Build a step divider
  Widget _buildStepDivider(int stepIndex, int currentStepIndex) {
    final isActive = currentStepIndex > stepIndex;

    return Expanded(
      child: Container(
        height: 2,
        color: isActive ? AppColors.primary : Colors.grey[300],
      ),
    );
  }

  /// Get current step index from status message
  int _getCurrentStepIndex(String statusMessage) {
    final message = statusMessage.toLowerCase();
    if (message.contains('preparing') || message.contains('prepare')) {
      return 0;
    }
    if (message.contains('generating') || message.contains('generate')) {
      return 1;
    }
    if (message.contains('saving') || message.contains('save')) {
      return 2;
    }
    if (message.contains('verifying') || message.contains('verify')) {
      return 3;
    }
    if (message.contains('complete')) {
      return 4;
    }
    return 0;
  }
}
